import axios from "axios";

// Create an axios instance for the worker
const instance = axios.create({
  baseURL: process.env.REACT_APP_Server,
  withCredentials: true,
  headers: {
    "Content-Type": "application/json",
    "x-role": "recruiter",
  },
});

export const postToApi = async (data) => {
  try {
    // Set the authorization header for this specific request
    const config = {
      headers: {
        Authorization: data.data.requestOptions.authorization,
      },
    };

    // Make the API call using axios
    const response = await instance.post(
      "/evaluation_users_response_logs",
      data.data.data,
      config
    );

    // Send the response data back to the main thread
    postMessage({ type: "POST_RESPONSE", result: response.data });
  } catch (error) {
    postMessage({
      type: "POST_ERROR",
      error: error.response?.data?.message || error.message,
    });
  }
};
