import React, { useState, useEffect } from "react";
import { Navigate } from "react-router-dom";
import { isDashboardCandidateAuthenticated } from "../Helpers/CandidateDashboardValidation";

const AuthmiddlewareStudent = (props) => {
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        const authResult = await isDashboardCandidateAuthenticated();
        setIsAuthenticated(authResult);
      } catch (error) {
        console.error("Authentication check error:", error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthentication();
  }, []);

  if (isLoading) {
    return null;
  }

  if (!isAuthenticated) {
    return <Navigate to={{ pathname: "/candidate/login" }} />;
  }

  return <React.Fragment>{props.children}</React.Fragment>;
};

export default AuthmiddlewareStudent;
