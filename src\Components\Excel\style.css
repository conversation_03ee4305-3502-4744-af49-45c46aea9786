.univer-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Hide the menubar */
:global(.univer-menubar) {
  display: none;
}

.univer-sheet-bar {
  display: none !important;
}

.univer-workbench-container-sidebar {
  height: 400px !important;
}

.univer-name-ranges input {
  font-size: 14px !important;
}

.univer-button-primary {
  background-color: #c0ff06 !important;
  color: black !important;
  border-color: black !important;
  font-family: "Silka" !important;
  font-size: 14px !important;
}

.univer-button-primary:hover {
  background-color: black !important;
  color: #c0ff06 !important;
  border-color: black !important;
}

.univer-sheet-permission-alert-dialog-title {
  font-size: 0 !important;
}

/* Add custom title */
.univer-sheet-permission-alert-dialog-title::before {
  display: hidden;
}

/* Hide the original text */
.univer-sheet-permission-alert-dialog .univer-button-primary {
  font-size: 0 !important;
}

/* Add Close text */
.univer-sheet-permission-alert-dialog .univer-button-primary::after {
  content: "Close";
  font-size: 14px !important;
  font-family: "Silka" !important;
}

/* Target the p tag directly inside the dialog */
.univer-sheet-permission-alert-dialog p {
  font-size: 0 !important;
  position: relative;
}

.univer-sheet-permission-alert-dialog p::after {
  content: "The range is protected, and you do not have edit permission. As a candidate you only have permission to edit the yellow highlighted cell";
  font-size: 14px !important;
  font-family: "Silka" !important;
}

.univer-workbench-container-header {
  display: none !important;
}

.univer-formula-box {
  display: none !important;
}

.univer-formula-help-function {
  display: none !important;
}

.univer-formula-search-function {
  display: none !important;
}
