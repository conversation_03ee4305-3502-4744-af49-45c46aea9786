.univer-container {
  width: 100%;
  height: 100%;
  overflow: hidden;
}

/* Hide the menubar */
:global(.univer-menubar) {
  display: none;
}

.univer-sheet-bar {
  display: none !important;
}

.univer-workbench-container-sidebar {
  height: 400px !important;
}

.univer-name-ranges input {
  font-size: 14px !important;
}

.univer-button-primary {
  background-color: #c0ff06 !important;
  color: black !important;
  border-color: black !important;
  font-family: "Silka" !important;
  font-size: 14px !important;
}

.univer-button-primary:hover {
  background-color: black !important;
  color: #c0ff06 !important;
  border-color: black !important;
}

.univer-sheet-permission-alert-dialog-title {
  font-size: 0 !important;
}

/* Add custom title */
.univer-sheet-permission-alert-dialog-title::before {
  display: hidden;
}

/* Hide the original text */
.univer-sheet-permission-alert-dialog .univer-button-primary {
  font-size: 0 !important;
}

/* Add Close text */
.univer-sheet-permission-alert-dialog .univer-button-primary::after {
  content: "Close";
  font-size: 14px !important;
  font-family: "Silka" !important;
}

/* Target the p tag directly inside the dialog */
.univer-sheet-permission-alert-dialog p {
  font-size: 0 !important;
  position: relative;
}

.univer-sheet-permission-alert-dialog p::after {
  content: "The range is protected, and you do not have edit permission. As a candidate you only have permission to edit the yellow highlighted cell";
  font-size: 14px !important;
  font-family: "Silka" !important;
}

.univer-workbench-container-header {
  display: none !important;
}

.univer-formula-box {
  display: none !important;
}

.univer-formula-help-function {
  display: none !important;
}

.univer-formula-search-function {
  display: none !important;
}

/* Mobile keyboard prevention for disabled cells */
.mobile-excel-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

.mobile-excel-container input,
.mobile-excel-container textarea,
.mobile-excel-container [contenteditable] {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
  -webkit-touch-callout: none;
  -webkit-tap-highlight-color: transparent;
}

/* Prevent virtual keyboard on mobile for disabled cells */
@media (max-width: 768px) {
  .mobile-excel-container .univer-sheet-cell:not(.masked-cell) input,
  .mobile-excel-container .univer-sheet-cell:not(.masked-cell) textarea,
  .mobile-excel-container
    .univer-sheet-cell:not(.masked-cell)
    [contenteditable] {
    pointer-events: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
    -webkit-touch-callout: none;
    -webkit-tap-highlight-color: transparent;
  }

  /* Ensure masked cells (yellow highlighted) can still be edited */
  .mobile-excel-container .univer-sheet-cell.masked-cell input,
  .mobile-excel-container .univer-sheet-cell.masked-cell textarea,
  .mobile-excel-container .univer-sheet-cell.masked-cell [contenteditable] {
    pointer-events: auto;
    -webkit-user-select: text;
    -moz-user-select: text;
    -ms-user-select: text;
    user-select: text;
  }
}

/* Remove blue selection highlight when dragging to select area */
.univer-container {
  -webkit-user-select: none;
  -moz-user-select: none;
  -ms-user-select: none;
  user-select: none;
}

/* Disable selection highlighting for all sheet elements */
.univer-sheet-canvas,
.univer-sheet-cell,
.univer-sheet-row,
.univer-sheet-column,
.univer-workbench-container-content {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
  -webkit-touch-callout: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* Hide selection overlay/highlight - Comprehensive targeting */
.univer-sheet-selection,
.univer-sheet-selection-overlay,
.univer-selection-overlay,
.univer-sheet-range-selection,
.univer-range-selection,
.univer-selection,
.univer-sheet-selection-shape,
.univer-selection-shape,
.univer-sheet-selection-control,
.univer-selection-control,
.univer-sheet-selection-area,
.univer-selection-area,
.univer-sheet-highlight,
.univer-highlight,
.univer-sheet-range-highlight,
.univer-range-highlight,
.univer-sheet-cell-selection,
.univer-cell-selection,
.univer-sheet-multi-selection,
.univer-multi-selection {
  display: none !important;
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
  width: 0 !important;
  height: 0 !important;
}

/* Alternative approach - make selection transparent */
.univer-sheet-selection-border,
.univer-sheet-selection-fill,
.univer-selection-border,
.univer-selection-fill,
.univer-range-selection-border,
.univer-range-selection-fill,
.univer-sheet-selection-background,
.univer-selection-background,
.univer-sheet-selection-mask,
.univer-selection-mask {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  border-color: transparent !important;
  opacity: 0 !important;
  fill: transparent !important;
  stroke: transparent !important;
  stroke-width: 0 !important;
}

/* Target any element with selection-related classes */
[class*="selection"],
[class*="highlight"],
[class*="range"] {
  background: transparent !important;
  background-color: transparent !important;
  border: none !important;
  opacity: 0 !important;
}

/* Force hide any blue selection backgrounds */
.univer-container [style*="background-color: rgb(173, 216, 230)"],
.univer-container [style*="background-color: lightblue"],
.univer-container [style*="background-color: #add8e6"],
.univer-container [style*="background: rgb(173, 216, 230)"],
.univer-container [style*="background: lightblue"],
.univer-container [style*="background: #add8e6"],
.univer-container [style*="background-color: rgba(173, 216, 230"],
.univer-container [style*="background: rgba(173, 216, 230"] {
  background: transparent !important;
  background-color: transparent !important;
}

/* Disable text selection globally in Excel container */
.univer-container * {
  -webkit-user-select: none !important;
  -moz-user-select: none !important;
  -ms-user-select: none !important;
  user-select: none !important;
}

/* Nuclear option - hide ALL possible selection backgrounds */
.univer-container
  *:not([style*="background-color: rgb(255, 255, 0)"]):not(
    [style*="background: rgb(255, 255, 0)"]
  ) {
  background-image: none !important;
}

/* Target any possible blue selection colors */
.univer-container [style*="background-color: rgb("],
.univer-container [style*="background: rgb("] {
  background-color: transparent !important;
  background: transparent !important;
}

/* Specifically target common blue selection colors */
.univer-container [style*="background-color: rgb(0, 123, 255)"],
.univer-container [style*="background-color: rgb(0, 120, 215)"],
.univer-container [style*="background-color: rgb(70, 130, 180)"],
.univer-container [style*="background-color: rgb(100, 149, 237)"],
.univer-container [style*="background-color: rgb(135, 206, 235)"],
.univer-container [style*="background: rgb(0, 123, 255)"],
.univer-container [style*="background: rgb(0, 120, 215)"],
.univer-container [style*="background: rgb(70, 130, 180)"],
.univer-container [style*="background: rgb(100, 149, 237)"],
.univer-container [style*="background: rgb(135, 206, 235)"] {
  background-color: transparent !important;
  background: transparent !important;
}

/* Override any selection with ::selection pseudo-element */
.univer-container *::selection {
  background: transparent !important;
  background-color: transparent !important;
}

.univer-container *::-moz-selection {
  background: transparent !important;
  background-color: transparent !important;
}

/* Re-enable selection only for input fields in masked cells */
.univer-container input[style*="background-color: rgb(255, 255, 0)"],
.univer-container textarea[style*="background-color: rgb(255, 255, 0)"],
.univer-container
  [contenteditable][style*="background-color: rgb(255, 255, 0)"] {
  -webkit-user-select: text !important;
  -moz-user-select: text !important;
  -ms-user-select: text !important;
  user-select: text !important;
}

/* Allow selection pseudo-elements for masked cells */
.univer-container input[style*="background-color: rgb(255, 255, 0)"]::selection,
.univer-container
  textarea[style*="background-color: rgb(255, 255, 0)"]::selection,
.univer-container
  [contenteditable][style*="background-color: rgb(255, 255, 0)"]::selection {
  background: #316ac5 !important;
  background-color: #316ac5 !important;
}

.univer-container
  input[style*="background-color: rgb(255, 255, 0)"]::-moz-selection,
.univer-container
  textarea[style*="background-color: rgb(255, 255, 0)"]::-moz-selection,
.univer-container
  [contenteditable][style*="background-color: rgb(255, 255, 0)"]::-moz-selection {
  background: #316ac5 !important;
  background-color: #316ac5 !important;
}
