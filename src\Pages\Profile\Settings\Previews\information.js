import React, { useEffect, useRef, useState } from "react";
import { useNavigate, useLocation, useParams } from "react-router-dom";
import queryString from "query-string";
import { useQueryClient, useMutation, useQuery } from "@tanstack/react-query";
import "../../../Candidates/StudentsInformation/student.css";
import * as moment from "moment";
import { GoArrowRight } from "react-icons/go";
import videojs from "video.js";
import "video.js/dist/video-js.css";
import NoContentModal from "../../../../Components/Modals/NoContent";
import VideoJS from "../../../../Components/VideoPlayer";
import { useSelector } from "react-redux";
import { getCompanyDetails } from "../hooks/getCompanyDetails";

const InformationScreen = () => {
  const location = useLocation();
  const parsed = queryString.parse(location.search);
  const previewData = useSelector((state) => state.preview.setPreview);
  const [noContentModal, setNoContentNodal] = useState(false);
  const [isHovered, setIsHovered] = useState(false);
  const module_info = JSON?.parse(localStorage.getItem("module_info"));
  const theme = localStorage.getItem("video");
  const playerRef = useRef(null);
  const containerRef = useRef(null);
  const { id } = useParams();

  //#region closing tab on onClick
  const closeTab = () => {
    window.opener = null;
    window.open("", "_self");
    window.close();
  };
  //#endregion

  //#region getting Company Details
  const { data: companyData, isLoading: companyLoading  } = useQuery(
    ["company", id],
    () => getCompanyDetails(id)
  );
  //#endregion

  //#region Handle Video on modal
  const videoJsOptions = {
    autoplay: true,
    controls: true,
    responsive: true,
    fluid: true,
    sources: [
      {
        src: theme,
        type: "video/mp4",
      },
    ],
  };
  const handlePlayerReady = (player) => {
    playerRef.current = player;
    player.on("waiting", () => {
      videojs.log("player is waiting");
    });

    player.on("dispose", () => {
      videojs.log("player will dispose");
    });
  };
  //#endregion

  //#region Styling of continue button
  const buttonStyle = {
    background: isHovered ? previewData[1] : previewData[1],
    transition: "background-color 0.1s, transform 0.1s",
    transform: isHovered ? "scale(1.02)" : "scale(1)",
    color: previewData[2],
    border: `1px solid ${previewData[2]}`,
    fontFamily: "Silka",
  };

  const handleHover = () => {
    setIsHovered(true);
  };

  const handleLeave = () => {
    setIsHovered(false);
  };
  //#endregion

  //#region Opening Video Modal
  useEffect(() => {
    setNoContentNodal(true);
  }, []);
  //#endregion

  console.log(companyData, "companyDatacompanyData");
  document.title = "Information | Dexta";

  return (
    <>
      <div className="bg-bodyColor">
        <div className="mx-auto lg:container sm:max-md:mt-10 mt-10 2xl:mt-0">
          <div className="flex justify-center h-screen">
            <div className="m-auto w-5/6 rounded-md bg-white">
              <div className="rounded-lg">
                <div className="lg:p-8">
                  <div className="grid lg:grid-cols-2">
                    <div className="pt-10 pb-10 lg:pl-12 col-span-1 sm:px-4 lg:px-[80px]">
                      <h1
                        className="text-xl"
                        style={{ fontFamily: "Archia Semibold" }}
                      >
                        Hello{" "}
                        <b style={{ fontFamily: "Archia Bold" }}>
                          {companyData?.data[0]?.users[0]?.firstName}{" "}
                          {companyData?.data[0]?.users[0]?.lastName}
                        </b>
                      </h1>
                      <p
                        className="mt-5 text-[#767676]"
                        style={{ fontFamily: "Silka" }}
                      >
                        Thanks you for applying to{" "}
                        <b
                          style={{ fontFamily: "Silka Bold" }}
                          className="text-black"
                        >
                          {companyData?.data[0]?.companyName}
                        </b>{" "}
                        and welcome to our test.{" "}
                      </p>
                      <p
                        className="mt-2 text-[#767676]"
                        style={{ fontFamily: "Silka" }}
                      >
                        Completing it will give you a chance to show off your
                        skills and stand out from the crowd!
                      </p>
                      <p
                        className="mt-5 text-black"
                        style={{ fontFamily: "Silka" }}
                      >
                        Good luck!
                      </p>
                      <div className="flex relative md:w-1/3">
                        <button
                          className="inline-flex items-center w-full justify-center px-4 mt-5 py-4  hover:text-white text-white text-sm font-medium rounded-md"
                          style={buttonStyle}
                          onMouseEnter={handleHover}
                          onMouseLeave={handleLeave}
                          type="submit"
                          onClick={closeTab}
                        >
                          Get Started
                          <GoArrowRight
                            alt="Add Transaction Icon"
                            className="w-5 h-5 ml-2 icon-image"
                          />
                        </button>
                        <div
                          className="tooltip w-[8rem] font-medium text-center"
                          style={{ fontFamily: "Silka" }}
                        >
                          Primary color
                        </div>
                      </div>
                    </div>
                    <div
                      className="pt-10 pb-10 pl-2 sm:px-4 lg:px-[50px]"
                      style={{ fontFamily: "Silka" }}
                    >
                      <p className="font-bold text-lg">
                        A few things before you start:
                      </p>
                      <ul className="list-disc pl-5 pt-2 text-[#767676]">
                        <li className="mt-2">
                          This test consists of{" "}
                          <b className="text-black">5 {"modules"}</b>. It will
                          take approximately{" "}
                          <b className="text-black">30 minutes</b> to complete
                        </li>
                        <li className="mt-2">
                          The test is timed. A timer is shown as per module or
                          question
                        </li>
                        <li className="mt-2">
                          You are free to use a{" "}
                          <b className="text-black">
                            calculator, pen and paper
                          </b>
                          . Note this may only be required for some modules and
                          not all
                        </li>
                        <li className="mt-2">
                          Each test module will have a set of preview questions
                          which are shown before the official test. The preview
                          questions will give you a flavour of what the official
                          test is like
                        </li>
                        <li className="mt-2">
                          The official module which you will be assessed against
                          will start after the preview questions
                        </li>
                        <li className="mt-2">
                          Each test module will have its own prescribed time
                          limit. Be sure to monitor the timer as you are
                          completing the test
                        </li>
                        <li className="mt-2">
                          Please allow the use of your camera/webcam and do not
                          leave full-screen mode. Snapshots will be taken of you
                          periodically during the test. These measures are taken
                          to ensure fairness for everyone
                        </li>
                        <li className="mt-2">
                          Turn on your speakers or headphone (to play audio)
                        </li>
                        <li className="mt-2">
                          We recommend completing the test in one go
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      {companyData?.data[0]?.companyVideo !== null && (
        <NoContentModal
          noContentModal={noContentModal}
          setNoContentNodal={setNoContentNodal}
          customWidth="sm:w-full sm:max-w-4xl"
          heading = {`Important Video Message from ${companyData?.data[0]?.companyName}`}
          componentChild={
            <div ref={containerRef} className="p-2 video-container">
              <VideoJS options={videoJsOptions} onReady={handlePlayerReady} />
            </div>
          }
        />
      )}
    </>
  );
};

export default InformationScreen;
