import React, { useState } from "react";
import MainLogo from "../../../../Dexta_assets/signup_girl.png";
import { useFormik } from "formik";
import * as Yup from "yup";
import ".././auth.css";
import { useNavigate } from "react-router-dom";
import { loginUser } from "../../../Auth/hooks/loginUser";
import { useMutation } from "@tanstack/react-query";
import { useQueryClient } from "@tanstack/react-query";
import { toast } from "react-toastify";
import { ToastContainer, Zoom } from "react-toastify";
import TextField from "../../../../Components/Dexta/TextField/TextField";
import Loader from "react-loader-spinner";
import { forgetEmail } from "../../hooks/forgetEmail";
import { FaArrowRightLong } from "react-icons/fa6";
import CustomButton from "../../../../Components/CustomButton/CustomButton";

const ForgetPasswordCandidate = () => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  //#region validation with formik
  const validation = useFormik({
    enableReinitialize: true,
    initialValues: {
      email: "",
    },
    validationSchema: Yup.object({
      email: Yup.string().required("Email Required"),
    }),
    onSubmit: (values) => {
      let data = JSON.stringify({
        email: values?.email,
      });
      try {
        mutate(data);
      } catch (error) {
        //onError will automatically detect
      }
    },
  });
  //#endregion

  //#region login with candidate
  const { mutate, isLoading } = useMutation(forgetEmail, {
    onSuccess: (response) => {
      queryClient.invalidateQueries("/auth/forgot-password");
      navigate(
        `/candidate/forget-password-email-sent/?email=${validation?.values?.email}`
      );
    },
    onError: (error) => {
      const errorMessage = error?.response?.data?.message;
      if (Array.isArray(errorMessage)) {
        setTimeout(() => {
          toast.error(errorMessage[0], {
            toastId: "copy-success",
          });
        }, 500);
      } else if (typeof errorMessage === "string") {
        setTimeout(() => {
          toast.error(errorMessage, {
            toastId: "copy-success",
          });
        }, 500);
      } else {
        toast.error("An error occurred.", {
          toastId: "copy-success",
        });
      }
    },
  });
  //#endregion

  return (
    <>
      <ToastContainer
        position="top-center"
        transition={Zoom}
        autoClose={5000}
        hideProgressBar={false}
        newestOnTop={true}
        closeOnClick
        rtl={false}
        pauseOnFocusLoss
        draggable
        pauseOnHover
        theme="light"
      />

      <div className="sm:grid-cols-1 lg:grid-cols-2 grid bg-bgAuth h-screen">
        <div className="bg-black sm:hidden lg:block">
          <img src={MainLogo} className="w-full lg:h-full  lg:object-cover" />
        </div>
        <div className="bg-bgAuth flex justify-center flex-col md:pb-40">
          <div className="sm:w-5/6 lg:w-2/3 mx-auto">
            <h1
              className="text-2xl text-left"
              style={{ fontFamily: "Archia Semibold" }}
            >
              Forgot Password?
            </h1>
            <p
              className="text-coalColor text-left mt-5"
              style={{ fontFamily: "Silka" }}
            >
              No, worries, we’ll send you reset instructions.
            </p>
            <form
              onSubmit={(e) => {
                e.preventDefault();
                validation.handleSubmit();
                return false;
              }}
            >
              <div className="h-[4.7rem] mt-6">
                <TextField
                  name="email"
                  id="email"
                  type="email"
                  placeholder="Enter your email"
                  onChange={validation.handleChange}
                  onBlur={validation.handleBlur}
                  marginTop="mt-2"
                  value={validation.values.email || ""}
                />
                {validation.touched.email && validation.errors.email ? (
                  <div className="ml-1">
                    <p className="text-rose-500 fade-in-text-validations sm:text-xs md:text-sm">
                      {validation.errors.email}
                    </p>
                  </div>
                ) : null}
              </div>
              <CustomButton
                label="Reset Password"
                borderCustom="border border-black text-white"
                paddingY="0.7rem"
                hoverBgColor="#C0FF06"
                hoverTextColor="#252E3A"
                marginTop="mt-4"
                bgColor="#252E3A"
                iconR={FaArrowRightLong}
                noMarginIcon={false}
                autoLeftMargin="ml-auto"
                textMarginBotton="ml-auto"
                LoadingBtn={isLoading}
                loadingText="Resetting"
              />
            </form>
            <span
              className="text-coalColor text-sm text-center mt-8"
              style={{ fontFamily: "Silka" }}
            >
              Don't want to reset your password?{" "}
              <span
                style={{ fontFamily: "Silka Bold" }}
                className="text-coalColor font-medium cursor-pointer"
                onClick={() => navigate("/candidate/login")}
              >
                Return to Login
              </span>
            </span>
          </div>
        </div>
      </div>
    </>
  );
};

export default ForgetPasswordCandidate;
