import { useRef, useState } from "react";
import AvatarEditor from "react-avatar-editor";
import { Box, Modal, Slider, Button } from "@mui/material";

const boxStyle = {
  width: "300px",
  height: "300px",
  display: "flex",
  flexFlow: "column",
  justifyContent: "center",
  alignItems: "center",
};
const modalStyle = {
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
};

const AvatarModal = ({
  profileSrc,
  profileModalOpen,
  setProfileModalOpen,
  setProfilePreview,
  setAllowProfile,
  setProfileblobdata,
}) => {
  const [slideValue, setSlideValue] = useState(10);
  const cropRef = useRef(null);

  //handle save
  const handleSave = async () => {
    if (cropRef) {
      const dataUrl = cropRef.current.getImage().toDataURL();
      const result = await fetch(dataUrl);
      const blob = await result.blob();
      setProfilePreview(URL.createObjectURL(blob));
      setProfileModalOpen(false);
      setAllowProfile(true);
      setProfileblobdata(blob);
    }
  };

  return (
    <Modal sx={modalStyle} open={profileModalOpen}>
      <Box sx={boxStyle}>
        <AvatarEditor
          ref={cropRef}
          image={profileSrc}
          style={{ width: "100%", height: "100%" }}
          border={50}
          borderRadius={150}
          color={[0, 0, 0, 0.72]}
          scale={slideValue / 10}
          rotate={0}
        />

        {/* MUI Slider */}
        <Slider
          min={10}
          max={50}
          sx={{
            margin: "0 auto",
            width: "80%",
            color: "#4A9CB9",
          }}
          size="medium"
          defaultValue={slideValue}
          value={slideValue}
          onChange={(e) => setSlideValue(e.target.value)}
        />
        <Box
          sx={{
            display: "flex",
            padding: "10px",
            border: "3px solid white",
            background: "black",
          }}
        >
          <Button
            size="small"
            sx={{
              marginRight: "10px",
              color: "white",
              borderColor: "white",
              "&:hover": {
                borderColor: "#4A9CB9",
              },
            }}
            variant="outlined"
            onClick={(e) => setProfileModalOpen(false)}
          >
            cancel
          </Button>
          <Button
            sx={{
              background: "#4A9CB9",
              "&:hover": {
                background: "#4A9CB9",
              },
            }}
            size="small"
            variant="contained"
            onClick={handleSave}
          >
            Save
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default AvatarModal;
