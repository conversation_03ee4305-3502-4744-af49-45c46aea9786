import React, { useState } from "react";
import { GoArrowRight } from "react-icons/go";
import Suggestions from "./suggestions";
import { useNavigate } from "react-router-dom";

const Welcome = () => {
  const navigate = useNavigate();
  const [searchText, setSearchText] = useState("");
  return (
    <div className="w-full md:w-4/5 lg:w-3/4 xl:w-1/2 mx-auto container px-4 py-8 mt-[2rem]">
      <p className="text-2xl" style={{ fontFamily: "Archia Semibold" }}>
        Hello there!
      </p>
      <h1
        className="text-6xl font-bold mt-2"
        style={{ fontFamily: "Archia Bold" }}
      >
        Welcome to Dexta AI
      </h1>

      {/* Search input */}
      <div className="relative mt-8">
        <input
          type="text"
          className="w-full px-6 py-4 pr-16 border border-gray-300 rounded-full focus:outline-none focus:border-gray-400"
          placeholder="Need a test for a role? Type it here."
          value={searchText}
          style={{ fontFamily: "Silka" }}
          onChange={(e) => setSearchText(e.target.value)}
        />
        <button
          className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primaryGreen rounded-full p-3"
          onClick={() => navigate("/chatuser")}
        >
          <GoArrowRight className="text-xl" />
        </button>
      </div>
      <Suggestions />
    </div>
  );
};

export default Welcome;
