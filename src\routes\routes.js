import React, { useState, useEffect } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { isRecruiterAuthenticated } from "../Helpers/Auth";
import queryString from "query-string";

const Authmiddleware = (props) => {
  const [isAuthenticated, setIsAuthenticated] = useState(true); // Default to true to prevent flashing
  const [isLoading, setIsLoading] = useState(true);
  const location = useLocation();
  const parsed = queryString.parse(location.search);

  useEffect(() => {
    const checkAuthentication = async () => {
      try {
        const authResult = await isRecruiterAuthenticated();
        setIsAuthenticated(authResult);
      } catch (error) {
        console.error("Authentication check error:", error);
        setIsAuthenticated(false);
      } finally {
        setIsLoading(false);
      }
    };

    checkAuthentication();
  }, []);

  if (isLoading) {
    // You can return a loading indicator here if needed
    return null; // or return <LoadingSpinner /> if you have a loading component
  }

  if (!isAuthenticated) {
    if (parsed?.dsc) {
      const dsc_obj = {
        dsc: parsed?.dsc,
        package_id: parsed?.package_id,
        currency: parsed?.currency,
        interval: parsed?.interval,
      };
      localStorage.setItem("page", "settings");
      localStorage.setItem("DSC_OBJ", JSON.stringify(dsc_obj));
    }
    return <Navigate to={{ pathname: "/login" }} />;
  }

  // if (localStorage.getItem("page") === "settings") {
  //   return <Navigate to={{ pathname: "/settings" }} />;
  // }

  return <React.Fragment>{props.children}</React.Fragment>;
};

export default Authmiddleware;
