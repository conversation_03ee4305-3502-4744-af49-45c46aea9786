import https from "../../../https"

export const getQuestions = async (question) => {
    // const response = await https.get(`/questions/bySectionId/${question.ModuleID}?page=${question.pageQuestion}`)
    const response = await https.get(`/questions/evaluation/${question.evaluation}/section/${question.ModuleID}?page=${question.pageQuestion}&isPracticeQuestion=${question.isPractice}`)
    return response.data;
}