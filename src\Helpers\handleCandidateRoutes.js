import React, { useState, useEffect } from "react";
import { Navigate, useLocation } from "react-router-dom";
import { isDashboardCandidateAuthenticated } from "./CandidateDashboardValidation";

const HandleCandidateRoutes = (props) => {
  const location = useLocation();
  const Exam = localStorage.getItem("Exam");
  const current = localStorage.getItem("Current_screen");
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const checkAuthentication = async () => {
      if (Exam !== "Start") {
        try {
          const authResult = await isDashboardCandidateAuthenticated();
          setIsAuthenticated(authResult);
        } catch (error) {
          console.error("Authentication check error:", error);
          setIsAuthenticated(false);
          // Clean up localStorage on authentication failure
          localStorage.removeItem("CP-CANDIDATE-TOKEN");
          localStorage.removeItem("CP-CANDIDATE-ID");
        } finally {
          setIsLoading(false);
        }
      } else {
        setIsLoading(false);
      }
    };

    checkAuthentication();
  }, [Exam]);

  console.log(isAuthenticated, "hello ABC");
  // During loading, return null to prevent flashing
  if (isLoading) {
    return null;
  }

  if (Exam === "Start") {
    if (
      current === "Feedback" &&
      location.pathname !== "/feedback" &&
      location.pathname !== "/finished" &&
      location.pathname !== "/candidate/login" &&
      location.pathname !== "/candidate/create-account"
    ) {
      return <Navigate to={{ pathname: "/feedback" }} />;
    }
    return <React.Fragment>{props.children}</React.Fragment>;
  } else {
    if (isAuthenticated) {
      return <Navigate to={{ pathname: "/candidate/dashboard" }} />;
    } else {
      return <Navigate to={{ pathname: "/candidate/create-account" }} />;
    }
  }
};

export default HandleCandidateRoutes;
