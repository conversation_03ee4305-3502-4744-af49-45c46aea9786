import React, { useState, useRef, useEffect } from "react";
import { GoArrowRight } from "react-icons/go";

const Chat = () => {
  const [inputMessage, setInputMessage] = useState("");
  const messagesEndRef = useRef(null);

  // Sample conversation from the image
  const [messages, setMessages] = useState([
    {
      id: 1,
      text: "Hi, I need to create a test for a Private Equity role. Can you help?",
      sender: "user",
    },
    {
      id: 2,
      text: "Sure! What would you like to assess — technical knowledge, deal structuring, financial modeling, or all of them?",
      sender: "assistant",
    },
    {
      id: 3,
      text: "A bit of everything, but mainly financial modeling and understanding deal lifecycle.",
      sender: "user",
    },
    {
      id: 4,
      text: "Got it. What level is this role — entry, associate, or senior?",
      sender: "assistant",
    },
    {
      id: 5,
      text: "Associate level. Someone with 2-3 years of experience would be ideal.",
      sender: "user",
    },
    {
      id: 6,
      text: "Perfect. Should I include topics like LBO modeling, valuation methods, and exit strategies?",
      sender: "assistant",
    },
  ]);

  // Scroll to bottom of chat when messages change
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: "smooth" });
  }, [messages]);

  const handleSendMessage = (e) => {
    e.preventDefault();
    if (inputMessage.trim() === "") return;

    // Add user message
    const newMessage = {
      id: messages.length + 1,
      text: inputMessage,
      sender: "user",
    };

    setMessages([...messages, newMessage]);
    setInputMessage("");

    // Simulate assistant response (in a real app, this would be an API call)
    setTimeout(() => {
      const assistantResponse = {
        id: messages.length + 2,
        text: "I'll create a comprehensive test for an Associate-level Private Equity role focusing on financial modeling and deal lifecycle understanding.",
        sender: "assistant",
      };
      setMessages((prevMessages) => [...prevMessages, assistantResponse]);
    }, 1000);
  };

  return (
    <div className="flex flex-col w-full h-full">
      {/* Chat messages area */}
      <div className="flex-1 overflow-y-auto p-4">
        <div className="max-w-4xl mx-auto">
          {messages.map((message) => (
            <div
              key={message.id}
              className={`mb-6 flex ${
                message.sender === "user" ? "justify-end" : "justify-start"
              }`}
            >
              <div
                className={`rounded-2xl p-4 max-w-[80%] ${
                  message.sender === "user"
                    ? "bg-white border border-gray-200 text-black"
                    : "bg-gray-100 text-black"
                }`}
                style={{ fontFamily: "Silka" }}
              >
                {message.text}
              </div>
            </div>
          ))}
          <div ref={messagesEndRef} />
        </div>
      </div>

      {/* Input area */}
      <div className="border-t border-gray-200 p-4 bg-white">
        <div className="max-w-4xl mx-auto">
          <form onSubmit={handleSendMessage} className="relative">
            <input
              type="text"
              className="w-full px-6 py-4 pr-16 border border-gray-300 rounded-full focus:outline-none focus:border-gray-400"
              placeholder="Need a test for a role? Type it here."
              value={inputMessage}
              onChange={(e) => setInputMessage(e.target.value)}
              style={{ fontFamily: "Silka" }}
            />
            <button
              type="submit"
              className="absolute right-2 top-1/2 transform -translate-y-1/2 bg-primaryGreen rounded-full p-3"
            >
              <GoArrowRight className="text-xl" />
            </button>
          </form>
        </div>
      </div>
    </div>
  );
};

export default Chat;
