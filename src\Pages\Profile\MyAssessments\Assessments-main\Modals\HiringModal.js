import { useState } from "react";
import { Dialog } from "@headlessui/react";
import { useQueryClient } from "@tanstack/react-query";
import closeIcon from "../../../../../Dexta_assets/closeModal.png";
import CustomButton from "../../../../../Components/CustomButton/CustomButton";
import queryString from "query-string";
import { useLocation, useParams } from "react-router-dom";
import { useSelector } from "react-redux";
import Premium from "../../../../../Components/Modals/Premium";
import CustomizeHiringEmail from "../../../../../Components/CustomizeEmail/CustomizeHiringEmail";
import EmailConfirmation from "./EmailConfirmation";
import { useMutation } from "@tanstack/react-query";
import { updateHiringStatus } from "../hooks/updateHiringStatus";
import { toast } from "react-toastify";

export default function HiringModal({
  hiringOpen,
  setHiringOpen,
  hiringStatus,
  hiringLabel,
  typeHiring,
  checkboxText,
  firstName,
  lastName,
  uID,
  noCustomization,
  HiringType,
  selectedCandidates,
  setSelectedRows,
  setSelectAll,
  candidateData,
}) {
  const [customizeCustomModal, setCustomizeCustomModal] = useState(false);
  const [emailContent, setEmailContent] = useState("");
  const [emailSubject, setEmailSubject] = useState("");
  const [emailType, setEmailType] = useState("");
  const [premiumOpen, setPremiumOpen] = useState(false);
  const [emailConfirmationOpen, setEmailConfirmationOpen] = useState(false);
  const queryClient = useQueryClient();
  const { id } = useParams();
  const [sendAll, setSendAll] = useState("");

  // Mutation for direct status change without email
  const { mutate: directStatusChange, isLoading: statusChangeLoading } =
    useMutation(updateHiringStatus, {
      onSuccess: (response) => {
        queryClient.invalidateQueries("/assessment/candidate");
        queryClient.invalidateQueries(["assessment"]);
        toast.success("Hiring stage changed for candidate.", {
          toastId: "status-change-success",
          style: { width: "350px" },
        });
        setHiringOpen(false);
      },
      onError: () => {
        toast.error("Failed to change hiring stage.", {
          toastId: "status-change-error",
        });
      },
    });

  // Handle direct status change without email
  const handleDirectStatusChange = () => {
    let data = {
      IDS: {
        userID: uID,
        Assessment: id,
      },
      postData: JSON.stringify({
        status: typeHiring,
        // No email flags are set here
      }),
    };

    try {
      directStatusChange(data);
    } catch (err) {
      // react-query will handle error
    }
  };

  return (
    <Dialog
      open={hiringOpen}
      onClose={() => {
        setHiringOpen(false);
      }}
      className="fixed inset-0 z-40 flex items-center justify-center overflow-y-auto"
    >
      <CustomizeHiringEmail
        customizeCustomModal={customizeCustomModal}
        setCustomizeCustomModal={setCustomizeCustomModal}
        emailContent={emailContent}
        emailSubject={emailSubject}
        emailType={emailType}
        passCheck={true}
        setHiringOpen={setHiringOpen}
        firstName={noCustomization ? "Candidate" : firstName}
        lastName={lastName}
      />
      <EmailConfirmation
        EmailConfirmationOpen={emailConfirmationOpen}
        setEmailConfirmationOpen={setEmailConfirmationOpen}
        hiringStatus={hiringStatus}
        hiringLabel={hiringLabel}
        typeHiring={typeHiring}
        checkboxText={checkboxText}
        firstName={firstName}
        lastName={lastName}
        uID={uID}
        noCustomization={sendAll === true ? true : noCustomization}
        HiringType={sendAll === true ? "position" : HiringType}
        selectedCandidates={selectedCandidates}
        setSelectedRows={setSelectedRows}
        setSelectAll={setSelectAll}
        hiringOpen={hiringOpen}
        setHiringOpen={setHiringOpen}
        setSendAll={setSendAll}
        sendAll={sendAll}
        emailcandidateData={candidateData}
      />
      <Premium premiumOpen={premiumOpen} setPremiumOpen={setPremiumOpen} />
      <div className="fixed inset-0 bg-black bg-opacity-75" />
      <Dialog.Panel className="relative bg-white rounded-lg overflow-hidden shadow-lg transform transition-all sm:max-w-lg sm:w-full sm:h-auto">
        <div className="bg-white py-5 px-4">
          <p
            className="text-lg font-medium text-coalColor cursor-pointer hover:text-coalColor text-left"
            style={{ fontFamily: "Archia Bold" }}
          >
            {hiringStatus}
          </p>
          <img
            src={closeIcon}
            className="absolute top-3 right-5 z-20 w-6 h-6 cursor-pointer"
            onClick={() => {
              setHiringOpen(false);
            }}
            alt="Close"
          />
          <p className="mt-5" style={{ fontFamily: "Silka" }}>
            {hiringLabel}
          </p>
          <div className="mt-5">
            <div className="flex flex-row justify-end px-8 gap-5 mt-8">
              <CustomButton
                label="No"
                textSize="text-base"
                borderCustom="border border-coalColor text-white"
                bgColor="#252E3A"
                hoverBgColor="#C0FF06"
                hoverTextColor="#252E3A"
                widthButton="w-[6rem]"
                paddingY="0.3rem"
                textColor="black"
                onClickButton={() => {
                  if (typeHiring === "POSITION WITHDRAWN") {
                    setEmailConfirmationOpen(true);
                  } else {
                    setHiringOpen(false);
                  }
                }}
              />
              <CustomButton
                label="Yes"
                textSize="text-base"
                bgColor="#C0FF06"
                widthButton="w-[6rem]"
                textColor="black"
                borderCustom="border border-black text-coalColor"
                hoverBgColor="#252E3A"
                hoverTextColor="white"
                paddingY="0.3rem"
                disabledColor="#D3D5D8"
                disabledTextColor="#7C8289"
                LoadingBtn={statusChangeLoading}
                loadingText={checkboxText && "Updating"}
                loadingColor="black"
                onClickButton={() => {
                  if (checkboxText === "") {
                    handleDirectStatusChange();
                  } else if (typeHiring === "POSITION WITHDRAWN") {
                    setEmailConfirmationOpen(true);
                    setSendAll(true);
                  } else {
                    setEmailConfirmationOpen(true);
                  }
                }}
              />
            </div>
          </div>
        </div>
      </Dialog.Panel>
    </Dialog>
  );
}
