import React from "react";
import { Line } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
} from "chart.js";
import annotationPlugin from "chartjs-plugin-annotation";

ChartJS.register(
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Tooltip,
  Legend,
  annotationPlugin
);

const PopulationGraphs2 = ({
  xArray,
  yArray,
  myScore,
  firstName,
  lastName,
}) => {
  const sortedData = xArray
    ?.map((score, index) => ({
      score,
      candidates: yArray[index],
    }))
    .sort((a, b) => a.score - b.score);

  const sortedScores = sortedData.map((data) => data.score);
  const sortedCandidates = sortedData.map((data) => data.candidates);

  const data = {
    labels: sortedScores,
    datasets: [
      {
        label: "Number of Candidates",
        data: sortedCandidates,
        borderColor: "orange",
        backgroundColor: "rgba(255, 165, 0, 0.2)",
        fill: true,
        tension: 0.4,
      },
      {
        label: `${firstName} ${lastName} Time`,
        data: [{ x: myScore, y: 0 }],
        pointBackgroundColor: "#FF5812",
        pointBorderColor: "#FF5812",
        pointRadius: 5,
        pointHoverRadius: 7,
        showLine: false,
        fill: false,
      },
    ],
  };

  const options = {
    responsive: true,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          title: (context) => {
            return context[0].dataset.label === `${firstName} ${lastName} Time`
              ? `${firstName} ${lastName}`
              : `Time: ${myScore}%`;
          },
          label: (context) => {
            return context.dataset.label === `${firstName} ${lastName} Time`
              ? `time: ${myScore}%`
              : `Number of Candidates: ${context.raw}`;
          },
        },
      },
      annotation: {
        annotations: {
          quartile1: {
            type: "line",
            xMin: 25,
            xMax: 25,
            borderColor: "black",
            borderWidth: 1,
            label: {
              content: "Quartile (Q1)",
              enabled: true,
              position: "start",
            },
          },
          quartile2: {
            type: "line",
            xMin: 50,
            xMax: 50,
            borderColor: "black",
            borderWidth: 1,
            label: {
              content: "Quartile (Q2)",
              enabled: true,
              position: "start",
            },
          },
          quartile3: {
            type: "line",
            xMin: 75,
            xMax: 75,
            borderColor: "black",
            borderWidth: 1,
            label: {
              content: "Quartile (Q3)",
              enabled: true,
              position: "start",
            },
          },
        },
      },
      legend: {
        display: true,
        position: "bottom",
        labels: {
          usePointStyle: true,
          pointStyle: "circle",
          radius: 10,
          borderWidth: 2,
          font: {
            size: 14,
            family: "Silka",
          },
          color: "black",
        },
        padding: 20,
        margin: {
          left: 20,
        },
      },
      title: {
        display: true,
        text: `Completion Time Insights`,
        font: {
          size: 18,
          family: "Archia Semibold",
        },
        color: "black",
      },
    },
    scales: {
      x: {
        type: "linear",
        min: 0,
        max: 100,
        title: {
          display: true,
          text: "% of allotted time used by candidate",
          font: {
            size: 16,
            weight: "bold",
            family: "Silka",
          },
          color: "black",
        },
      },
      y: {
        beginAtZero: true,
        title: {
          display: true,
          font: {
            size: 16,
            weight: "bold",
            family: "Silka",
          },
          color: "black",
          text: "Number of Candidates",
        },
        ticks: {
          callback: (value) => {
            if (Number.isInteger(value)) {
              return value;
            }
          },
        },
      },
    },
  };

  return (
    <div>
      <Line data={data} options={options} />
    </div>
  );
};

export default PopulationGraphs2;
