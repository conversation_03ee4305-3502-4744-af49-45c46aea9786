import React from "react";
import { Link } from "react-router-dom";
import { IoSettingsOutline } from "react-icons/io5";
import { FiDownload } from "react-icons/fi";
import Logo from "../../Dexta_assets/logodexta2.png";
import Scrollbars from "react-custom-scrollbars";
import CollapseImage from "../../Assets/collapseSidebar.png";

const ChatSidebar = ({ isOpen, toggleSidebar, setSidebarOpen }) => {
  const recentChats = [
    { id: 1, title: "Corporate Banking Operations LI" },
    { id: 2, title: "Corporate Banking" },
    { id: 3, title: "Corporate Banking Operations LI" },
    { id: 4, title: "Corporate Bank" },
    { id: 5, title: "Corporate Banking Operations" },
  ];

  const olderChats = [
    { id: 6, title: "Corporate Banking Operations LI" },
    { id: 7, title: "Corporate Banking" },
    { id: 8, title: "Corporate Banking Operations LI" },
    { id: 9, title: "Corporate Bank" },
    { id: 10, title: "Corporate Banking Operations" },
  ];
  return (
    <>
      <div
        className={`fixed top-0 left-0 h-full bg-white border-r border-gray-200 transition-all duration-300 z-50 ${
          isOpen ? "w-80" : "w-0 -translate-x-full"
        } overflow-hidden`}
      >
        <div className="flex flex-col h-full">
          <div
            className="p-4 border-b border-gray-200 flex items-center
        justify-between"
          >
            <img
              src={Logo}
              className="h-[50px] w-[190px] object-fill"
              alt="CP Logo"
            />
            <button
              onClick={() => setSidebarOpen(false)}
              className="text-gray-700 hover:text-gray-900 mr-4"
            >
              <img src={CollapseImage} alt="collapse" className="h-10 w-10" />
            </button>
          </div>

          <div className="flex-1 overflow-hidden">
            <Scrollbars autoHide style={{ width: "100%", height: "100%" }}>
              <div className="p-4">
                <h3
                  className="text-sm font-medium text-gray-900 mb-2"
                  style={{ fontFamily: "Archia Semibold" }}
                >
                  Today
                </h3>
                <ul className="space-y-1">
                  {recentChats.map((chat) => (
                    <li key={chat.id}>
                      <Link
                        to={`/chat/${chat.id}`}
                        className="block text-sm text-gray-700 hover:bg-gray-100 rounded p-2"
                        style={{ fontFamily: "Silka" }}
                      >
                        {chat.title}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>

              <div className="p-4">
                <h3
                  className="text-sm font-medium text-gray-900 mb-2"
                  style={{ fontFamily: "Archia Semibold" }}
                >
                  Last 7 days
                </h3>
                <ul className="space-y-1">
                  {olderChats.map((chat) => (
                    <li key={chat.id}>
                      <Link
                        to={`/chat/${chat.id}`}
                        className="block text-sm text-gray-700 hover:bg-gray-100 rounded p-2"
                        style={{ fontFamily: "Silka" }}
                      >
                        {chat.title}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            </Scrollbars>
          </div>

          {/* Footer with settings and download */}
          <div className="p-4 border-t border-gray-200">
            <ul className="space-y-2">
              <li>
                <Link
                  to="/settings"
                  className="flex items-center text-sm text-gray-700 hover:bg-gray-100 rounded p-2"
                  style={{ fontFamily: "Silka" }}
                >
                  <IoSettingsOutline className="mr-2" />
                  Setting
                </Link>
              </li>
              <li>
                <Link
                  to="/download"
                  className="flex items-center text-sm text-gray-700 hover:bg-gray-100 rounded p-2"
                  style={{ fontFamily: "Silka" }}
                >
                  <FiDownload className="mr-2" />
                  Download
                </Link>
              </li>
            </ul>
          </div>
        </div>
      </div>
    </>
  );
};

export default ChatSidebar;
