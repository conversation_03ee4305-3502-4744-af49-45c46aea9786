import { useRef, useState } from "react";
import AvatarEditor from "react-avatar-editor";
import { Box, Modal, Slider, Button } from "@mui/material";

const boxStyle = {
  width: "600px",
  height: "300px",
  display: "flex",
  flexFlow: "column",
  justifyContent: "center",
  alignItems: "center",
};
const modalStyle = {
  display: "flex",
  justifyContent: "center",
  alignItems: "center",
};

const CropperModal = ({
  src,
  modalOpen,
  setModalOpen,
  setPreview,
  setAllow,
  setblobdata,
}) => {
  const [slideValue, setSlideValue] = useState(10);
  const cropRef = useRef(null);

  //handle save
  const handleSave = async () => {
    if (cropRef) {
      const dataUrl = cropRef.current.getImage().toDataURL();
      const result = await fetch(dataUrl);
      const blob = await result.blob();
      setPreview(URL.createObjectURL(blob));
      setModalOpen(false);
      setAllow(true);
      setblobdata(blob);
    }
  };

  return (
    <Modal sx={modalStyle} open={modalOpen}>
      <Box sx={boxStyle}>
        <AvatarEditor
          ref={cropRef}
          image={src}
          style={{ width: "100%", height: "100%" }}
          width={2200} // Set the desired width
          height={500} // Set the desired height
          border={50}
          borderRadius={0}
          color={[0, 0, 0, 0.72]}
          scale={slideValue / 15}
          rotate={0}
        />

        {/* MUI Slider */}
        <Slider
          min={10}
          max={50}
          sx={{
            margin: "0 auto",
            width: "80%",
            color: "#4A9CB9",
          }}
          size="medium"
          defaultValue={slideValue}
          value={slideValue}
          onChange={(e) => setSlideValue(e.target.value)}
        />
        <Box
          sx={{
            display: "flex",
            padding: "10px",
            border: "3px solid white",
            background: "black",
          }}
        >
          <Button
            size="small"
            sx={{
              marginRight: "10px",
              color: "white",
              borderColor: "white",
              "&:hover": {
                borderColor: "#4A9CB9",
              },
            }}
            variant="outlined"
            onClick={(e) => setModalOpen(false)}
          >
            cancel
          </Button>
          <Button
            sx={{
              background: "#4A9CB9",
              "&:hover": {
                background: "#4A9CB9",
              },
            }}
            size="small"
            variant="contained"
            onClick={handleSave}
          >
            Save
          </Button>
        </Box>
      </Box>
    </Modal>
  );
};

export default CropperModal;
