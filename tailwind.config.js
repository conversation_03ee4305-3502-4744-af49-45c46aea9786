/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ["./src/**/*.{js,ts,jsx,tsx}"],
  theme: {
    container: {
      center: true,
      screens: {
        sm: "300px",
        // => @media (min-width: 640px) { ... }
        mid: "500px",
        md: "768px",
        // => @media (min-width: 768px) { ... }

        lg: "1024px",
        // => @media (min-width: 1024px) { ... }
        lp1: "900px",
        lp2: "1000px",

        xl: "1280px",
        // => @media (min-width: 1280px) { ... }

        xll: "1330px",
        // => @media (min-width: 1280px) { ... }

        "2xl": "1536px",
        // => @media (min-width: 1536px) { ... }

        "3xl": "1700px",
        // => @media (min-width: 1536px) { ... }
        "4xl": "2160px",
      },
    },
    screens: {
      sm: "300px",
      // => @media (min-width: 640px) { ... }
      mid: "500px",
      md: "768px",
      // => @media (min-width: 768px) { ... }
      lp1: "900px",
      lp2: "1000px",
      lg: "1024px",
      // => @media (min-width: 1024px) { ... }

      xl: "1280px",
      // => @media (min-width: 1280px) { ... }

      xxl: "1330px",
      // => @media (min-width: 1280px) { ... }

      "2xl": "1536px",
      // => @media (min-width: 1536px) { ... }

      "3xl": "1700px",
      // => @media (min-width: 1536px) { ... }
    },
    extend: {
      keyframes: {
        wiggle: {
          "0%, 100%": { transform: "rotate(-3deg)" },
          "50%": { transform: "rotate(3deg)" },
        },
        jiggle: {
          "0%, 100%": { transform: "scale(1.02)" },
          "50%": { transform: "scale(1.02)" },
        },
      },
      animation: {
        wiggle: "wiggle 1s ease-in-out infinite",
        jiggle: "jiggle 1s ease-in-out infinite",
      },
      colors: {
        bgAppPrimary: "#131313",
        bgAppBlackSecondary: "#1D1D1D",
        bgAppInput: "#252525",
        bgAppBtnColor: "#4A9CB9",
        bgAppBtnDiscordColor: "#5865F2",
        bgAppBtnFacebookColor: "#1877F2",
        bgAuth: "#DBD9C0",
        bgDextaPrimary: "#C0FF06",
        coalColor: "#252E3A",
        primaryGreen: "#C0FF06",
        bodyColor: "#F6F7F7",
        disabledColor: "#D3D5D8",
        alertRed: "#F00000",
      },
      height: {
        26: "26rem",
        32: "31.5rem",
        125: "10rem",
        1001: "4rem",
        1002: "14rem",
        1003: "12.9rem",
        1004: "4rem",
        1005: "8rem",
        1006: "5rem",
        1007: "13rem",
        1008: "60rem",
        1009: "9rem",
        1010: "0.1rem",
        1011: "3.2rem",
      },
      width: {
        32: "31.5rem",
        26: "26rem",
        32: "31.5rem",
        1003: "12.9rem",
        21: "21rem",
        14: "14rem",
        140: "40rem",
        125: "10rem",
        1002: "3.5rem",
        1003: "5rem",
        1004: "12.8rem",
        1005: "10rem",
        1006: "3.5rem",
        1007: "7.5rem",
        1008: "10rem",
        1009: "20rem",
        1010: "12rem",
        1011: "40rem",
        1012: "7.8rem",
        1013: "15.1rem",
      },
      left: {
        47: "47rem",
      },
    },
  },
  variants: {
    extend: {
      scale: ["focus-within"],
      animation: ["motion-safe"],
    },
  },
  plugins: [],
};
