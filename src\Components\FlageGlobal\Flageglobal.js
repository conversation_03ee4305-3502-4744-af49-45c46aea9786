import React, { useEffect, useRef, useState } from "react";
import { FaChevronDown } from "react-icons/fa";
import Global from "../../Dexta_assets/globalicon.png";
import Saudi from "../../Dexta_assets/flag-round-250.png";

const Flageglobal = ({ onSelectFlag, isTop, isRight, isbottom }) => {
  const [selectedFlag, setSelectedFlag] = useState("global");
  const [open, setOpen] = useState(false);
  const isSaudiDomain = window.location.hostname.includes("app-sa.dexta.io");
  const dropdownRef = useRef(null);
  const flags = {
    global: { name: "Global", image: Global },
    saudi: { name: "Saudi Arabia", image: Saudi },
  };

  const toggleDropdown = () => {
    setOpen((prev) => !prev);
  };

  const handleSelect = (flagKey) => {
    if (flagKey === "saudi") {
      window.location.href = process.env.REACT_APP_STATIC_SITE;
      return;
    }
    setSelectedFlag(flagKey);
    setOpen(false);
    onSelectFlag && onSelectFlag(flagKey); // pass to parent if exists
  };
  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpen(false);
      }
    };
    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);
  return (
    <div
      ref={dropdownRef}
      className={`relative  ${isTop ? isTop : "top-7"} ${
        isRight ? isRight : "right-[90px]"
      } ${isbottom ? isbottom : "bottom-0"} cursor-pointer`}
    >
      {/* Button */}
      <button
        onClick={toggleDropdown}
        className="flex rounded-md items-center h-[42px] w-[175px] space-x-2 px-3 py-2  border bg-white shadow"
      >
        <img
          src={flags[selectedFlag].image}
          alt={flags[selectedFlag].name}
          className="w-6 object-cover"
        />
        <span className="text-sm font-normal" style={{ fontFamily: "Silka" }}>
          {flags[selectedFlag].name}
        </span>
      </button>
      {!isSaudiDomain && (
        <div className="absolute top-[15px] right-[12px] cursor-pointer">
          <FaChevronDown
            onClick={toggleDropdown}
            className="text-gray-500 text-xs"
          />
        </div>
      )}
      {/* Dropdown */}
      {open && (
        <div className="absolute mt-[3px] w-full bg-white border rounded-md shadow-lg z-10">
          <ul>
            {Object.entries(flags)
              .filter(([key]) => key !== selectedFlag)
              .map(([key, { name, image }]) => (
                <li
                  key={key}
                  className="px-4 py-2 hover:bg-gray-100 cursor-pointer flex items-center space-x-2"
                  onClick={() => handleSelect(key)}
                >
                  <img src={image} alt={name} className="w-6" />
                  <span
                    className="text-sm font-normal"
                    style={{ fontFamily: "Silka" }}
                  >
                    {name}
                  </span>
                </li>
              ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default Flageglobal;
