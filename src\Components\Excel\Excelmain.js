import {
  createUniver,
  defaultTheme,
  LocaleType,
  merge,
} from "@univerjs/presets";
import { UniverSheetsCorePreset } from "@univerjs/presets/preset-sheets-core";
import UniverPresetSheetsCoreEnUS from "@univerjs/presets/preset-sheets-core/locales/en-US";

import "./style.css";
import "@univerjs/presets/lib/styles/preset-sheets-core.css";

import { BooleanNumber, SheetTypes } from "@univerjs/core";
import { LocaleType as CoreLocaleType } from "@univerjs/core";
import { useEffect, useRef, useState } from "react";
import { toast, ToastContainer, Zoom } from "react-toastify";
import "react-toastify/dist/ReactToastify.css";

// Utility function to detect mobile devices
const isMobileDevice = () => {
  return (
    /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(
      navigator.userAgent
    ) ||
    (window.innerWidth <= 768 && "ontouchstart" in window)
  );
};

// Convert A-Z column letters to number index (0-based)
export const letterToColumn = (letter) => {
  let col = 0;
  for (let i = 0; i < letter.length; i++) {
    col = col * 26 + (letter.charCodeAt(i) - 65 + 1);
  }
  return col - 1;
};

// Convert number index to A-Z column letter
export const columnToLetter = (col) => {
  let letter = "";
  while (col >= 0) {
    letter = String.fromCharCode((col % 26) + 65) + letter;
    col = Math.floor(col / 26) - 1;
  }
  return letter;
};

// Convert "A1" -> { rowIndex, colIndex }
export const cellRefToIndices = (cellRef) => {
  const match = cellRef.match(/([A-Z]+)(\d+)/);
  if (!match) return null;
  const [, colLetter, rowNumber] = match;
  const rowIndex = parseInt(rowNumber, 10) - 1;
  const colIndex = letterToColumn(colLetter);
  return { rowIndex, colIndex };
};

// API → Univer Matrix format
export const transformApiToMatrix = (apiData) => {
  const cellData = {};
  let maxRow = 0;
  let maxCol = 0;

  if (apiData?.excelData) {
    Object?.entries(apiData?.excelData).forEach(([cell, value]) => {
      const { rowIndex, colIndex } = cellRefToIndices(cell);
      if (!cellData[rowIndex]) cellData[rowIndex] = {};
      cellData[rowIndex][colIndex] = { v: value };
      maxRow = Math.max(maxRow, rowIndex);
      maxCol = Math.max(maxCol, colIndex);
    });
  }

  if (Array?.isArray(apiData?.maskedCells)) {
    apiData?.maskedCells.forEach((cellRef) => {
      const { rowIndex, colIndex } = cellRefToIndices(cellRef);
      if (!cellData[rowIndex]) cellData[rowIndex] = {};
      if (!cellData[rowIndex][colIndex]) cellData[rowIndex][colIndex] = {};

      // Always apply yellow highlighting to all masked cells with left text alignment
      cellData[rowIndex][colIndex].s = {
        bg: { rgb: "#FFFF00" },
        ht: 1, // 1 = left alignment
      };

      // When highlightAllMaskedCells is true:
      // Clear the cell value (make it empty) to hide any data
      if (apiData?.highlightAllMaskedCells) {
        // Clear the cell value to hide any data
        cellData[rowIndex][colIndex].v = "";
      }
    });
  }

  return {
    id: "workbook-01",
    name: "universheet",
    sheetOrder: ["sheet-01"],
    sheets: {
      "sheet-01": {
        type: SheetTypes.GRID,
        id: "sheet-01",
        name: "Sheet 1",
        tabColor: "blue",
        cellData,
        hidden: BooleanNumber.FALSE,
        rowCount: Math.max(maxRow + 1, 16),
        columnCount: Math.max(maxCol + 1, 16),
        defaultRowHeight: 28,
        defaultColumnWidth: 93,
        rowHeader: { width: 46, hidden: BooleanNumber.FALSE },
        columnHeader: { height: 20, hidden: BooleanNumber.FALSE },
        selections: ["A1"],
        zoomRatio: 1,
        scrollTop: 0,
        scrollLeft: 0,
        showGridlines: 1,
        status: 1,
        hideRow: [],
        hideColumn: [],
        pluginMeta: {},
        rightToLeft: BooleanNumber.FALSE,
      },
    },
  };
};

// Matrix → API format
export const transformMatrixToApi = (cellData) => {
  const apiExcelData = {};
  Object.entries(cellData).forEach(([rowIndex, row]) => {
    Object.entries(row).forEach(([colIndex, cell]) => {
      const colLetter = columnToLetter(parseInt(colIndex));
      const rowNumber = parseInt(rowIndex) + 1;
      apiExcelData[`${colLetter}${rowNumber}`] = String(cell.v);
    });
  });
  return { excelData: apiExcelData };
};

// Main Component
// ... (imports remain unchanged)

/**
 * Excel Sheets Component
 * @param {Object} props - Component props
 * @param {Object} props.cellsData - Excel cell data
 * @param {Array} props.maskedCells - Array of masked cell references (e.g. ["A1", "B2"])
 * @param {Function} props.SetExcelApiData - Function to set Excel API data
 * @param {Function} props.SetCellsFilled - Function to set if cells are filled
 * @param {string} props.excelID - ID for the Excel component
 * @param {Object} [props.responseSubmitted] - Previously submitted response data
 * @param {boolean} [props.highlightAllMaskedCells=false] - When true, highlights all masked cells even if they don't have data
 */
export default function ExcelSheets({
  cellsData,
  maskedCells,
  SetExcelApiData,
  SetCellsFilled,
  excelID,
  responseSubmitted,
  highlightAllMaskedCells = false,
}) {
  const [workbookData, setWorkbookData] = useState(null);
  const univerCreatedRef = useRef(false);
  const univerAPIRef = useRef(null);
  const workbookDataRef = useRef(null);

  useEffect(() => {
    const matrix = transformApiToMatrix({
      excelData: responseSubmitted || cellsData,
      maskedCells,
      highlightAllMaskedCells,
    });

    const initialWorkbook = {
      ...matrix,
      locale: CoreLocaleType.EN_US,
      appVersion: "3.0.0-alpha",
    };

    setWorkbookData(initialWorkbook);
    workbookDataRef.current = initialWorkbook;
    univerCreatedRef.current = false;
  }, [responseSubmitted, cellsData, maskedCells, highlightAllMaskedCells]);

  useEffect(() => {
    if (!workbookData || univerCreatedRef.current) return;

    const { univerAPI } = createUniver({
      locale: LocaleType.EN_US,
      locales: {
        [LocaleType.EN_US]: merge({}, UniverPresetSheetsCoreEnUS),
      },
      theme: defaultTheme,
      presets: [UniverSheetsCorePreset()],
    });

    univerAPIRef.current = univerAPI;
    univerAPI.createWorkbook(workbookData);

    const initialCellData = workbookData.sheets["sheet-01"].cellData;
    SetExcelApiData(transformMatrixToApi(initialCellData));

    // When highlightAllMaskedCells is true, we consider all cells as not filled
    // since we're hiding any data that might be in them
    if (highlightAllMaskedCells) {
      SetCellsFilled(false);
    } else {
      // Normal behavior - check if any masked cells have data
      let anyMaskedFilled = false;
      for (const cellRef of maskedCells) {
        const { rowIndex, colIndex } = cellRefToIndices(cellRef);
        const cell = initialCellData?.[rowIndex]?.[colIndex];
        if (cell?.v !== undefined && cell.v !== null && cell.v !== "") {
          anyMaskedFilled = true;
          break;
        }
      }
      SetCellsFilled(anyMaskedFilled);
    }

    // 🔒 Lock all cells except masked ones
    const restrictPermissionOnCells = async () => {
      const book = univerAPI?.getActiveUniverSheet();
      const sheet = book.getActiveSheet();

      const bookId = book.getId();
      const sheetId = sheet.getSheetId();
      const permission = book.getPermission();

      const allCells = [];
      for (let row = 1; row <= 100; row++) {
        for (let col = 0; col < 26; col++) {
          const cell = `${String.fromCharCode(65 + col)}${row}`;
          allCells.push(cell);
        }
      }

      const cellsToLock = allCells.filter(
        (cell) => !maskedCells.includes(cell)
      );
      const ranges = cellsToLock.map((cell) => sheet.getRange(cell));

      const { permissionId, ruleId } = await permission.addRangeBaseProtection(
        bookId,
        sheetId,
        ranges
      );

      const rangeProtectionPermissionEditPoint =
        permission.permissionPointsDefinition
          .RangeProtectionPermissionEditPoint;

      permission.rangeRuleChangedAfterAuth$.subscribe((currentPermissionId) => {
        if (currentPermissionId === permissionId) {
          permission.setRangeProtectionPermissionPoint(
            bookId,
            sheetId,
            permissionId,
            rangeProtectionPermissionEditPoint,
            false
          );
        }
      });
    };

    restrictPermissionOnCells();

    // 🟡 Keep the existing edit logic
    univerAPI.addEvent(univerAPI.Event.SheetEditChanging, (params) => {
      const newValue = params?.value?._data?.body?.dataStream;
      const row = params?.row;
      const column = params?.column;

      if (newValue === undefined || row === undefined || column === undefined)
        return;

      const trimmedValue = String(newValue).trim();
      const refWorkbook = workbookDataRef.current;
      const cellData = refWorkbook?.sheets?.["sheet-01"]?.cellData || {};
      const updatedCellData = { ...cellData };

      if (!updatedCellData[row]) updatedCellData[row] = {};
      const existingCell = updatedCellData[row][column] || {};

      // Check if this is a masked cell
      const isMaskedCell = maskedCells.some((cellRef) => {
        const { rowIndex, colIndex } = cellRefToIndices(cellRef);
        return rowIndex === row && colIndex === column;
      });

      if (isMaskedCell) {
        // For masked cells
        if (highlightAllMaskedCells) {
          // When highlightAllMaskedCells is true:
          // 1. Always apply yellow highlighting
          // 2. Keep the cell value empty to hide any data
          updatedCellData[row][column] = {
            ...existingCell,
            v: "", // Keep the cell empty regardless of user input
            t: 1,
            s: {
              bg: { rgb: "#FFFF00" }, // Always highlight
              ht: 1, // 1 = left alignment
            },
          };
        } else {
          // When highlightAllMaskedCells is false:
          // 1. Always apply yellow highlighting
          // 2. Allow the cell to show user input
          updatedCellData[row][column] = {
            ...existingCell,
            v: trimmedValue,
            t: 1,
            s: {
              bg: { rgb: "#FFFF00" }, // Always highlight
              ht: 1, // 1 = left alignment
            },
          };
        }
      } else {
        // For non-masked cells, normal behavior
        updatedCellData[row][column] = {
          ...existingCell,
          v: trimmedValue,
          t: 1,
          s: existingCell?.s,
        };
      }

      workbookDataRef.current = {
        ...refWorkbook,
        sheets: {
          ...refWorkbook.sheets,
          ["sheet-01"]: {
            ...refWorkbook.sheets["sheet-01"],
            cellData: updatedCellData,
          },
        },
      };

      SetExcelApiData(transformMatrixToApi(updatedCellData));

      // When highlightAllMaskedCells is true, we consider all cells as not filled
      // since we're hiding any data that might be in them
      if (highlightAllMaskedCells) {
        SetCellsFilled(false);
      } else {
        // Normal behavior - check if any masked cells have data
        let anyFilled = false;
        for (const cellRef of maskedCells) {
          const { rowIndex, colIndex } = cellRefToIndices(cellRef);
          const cell = updatedCellData?.[rowIndex]?.[colIndex];
          if (cell?.v !== undefined && cell.v !== null && cell.v !== "") {
            anyFilled = true;
            break;
          }
        }
        SetCellsFilled(anyFilled);
      }
    });

    // 📱 SUPER SIMPLE: Prevent mobile keyboard on disabled cells
    if (isMobileDevice()) {
      // Add mobile-specific CSS class to container
      const container = document.getElementById(excelID);
      if (container) {
        container.classList.add("mobile-excel-container");
      }

      // 🔥 NUCLEAR APPROACH: Prevent keyboard on ALL inputs in Excel, except yellow cells
      const nuclearKeyboardPrevention = () => {
        const container = document.getElementById(excelID);
        if (!container) return;

        // Function to check if an element is in a yellow (masked) cell
        const isInYellowCell = (element) => {
          // Check the element itself
          if (
            element.style.backgroundColor?.includes("255, 255, 0") ||
            element.style.background?.includes("255, 255, 0")
          ) {
            return true;
          }

          // Check parent elements up the tree
          let parent = element.parentElement;
          while (parent && parent !== container) {
            if (
              parent.style.backgroundColor?.includes("255, 255, 0") ||
              parent.style.background?.includes("255, 255, 0")
            ) {
              return true;
            }
            parent = parent.parentElement;
          }

          // Check if any ancestor has yellow background using computed styles
          const ancestors = [];
          let current = element;
          while (current && current !== container) {
            ancestors.push(current);
            current = current.parentElement;
          }

          for (const ancestor of ancestors) {
            const computedStyle = window.getComputedStyle(ancestor);
            const bgColor = computedStyle.backgroundColor;
            if (
              bgColor === "rgb(255, 255, 0)" ||
              bgColor === "rgba(255, 255, 0, 1)"
            ) {
              return true;
            }
          }

          return false;
        };

        // Handle focus events
        const handleFocus = (event) => {
          const target = event.target;

          // Only handle input elements
          if (
            target.tagName === "INPUT" ||
            target.tagName === "TEXTAREA" ||
            target.contentEditable === "true"
          ) {
            // Check if this input is inside the Excel container
            if (container.contains(target)) {
              // If it's NOT in a yellow cell, prevent keyboard
              if (!isInYellowCell(target)) {
                console.log("🚫 Preventing keyboard on disabled cell");
                event.preventDefault();
                event.stopPropagation();

                // Immediately blur the element
                target.blur();

                // Set attributes to prevent keyboard
                target.setAttribute("readonly", "readonly");
                target.setAttribute("inputmode", "none");
                target.setAttribute("tabindex", "-1");

                // Remove focus styles
                target.style.outline = "none";
                target.style.border = "none";

                // Remove attributes after a delay
                setTimeout(() => {
                  target.removeAttribute("readonly");
                  target.removeAttribute("inputmode");
                  target.removeAttribute("tabindex");
                }, 100);

                return false;
              } else {
                console.log("✅ Allowing keyboard on yellow cell");
              }
            }
          }
        };

        // Handle click events
        const handleClick = (event) => {
          const target = event.target;

          // If clicking anywhere in the Excel container
          if (container.contains(target)) {
            setTimeout(() => {
              // Find any focused input elements
              const focusedElements = container.querySelectorAll(
                'input:focus, textarea:focus, [contenteditable="true"]:focus'
              );

              focusedElements.forEach((element) => {
                // If the focused element is NOT in a yellow cell, blur it
                if (!isInYellowCell(element)) {
                  console.log("🚫 Blurring focused element in disabled cell");
                  element.blur();
                  element.setAttribute("inputmode", "none");
                  setTimeout(() => {
                    element.removeAttribute("inputmode");
                  }, 50);
                }
              });
            }, 0);
          }
        };

        // Handle touchstart for mobile
        const handleTouchStart = (event) => {
          const target = event.target;

          if (container.contains(target)) {
            // If touching an input element that's not in a yellow cell
            if (
              (target.tagName === "INPUT" ||
                target.tagName === "TEXTAREA" ||
                target.contentEditable === "true") &&
              !isInYellowCell(target)
            ) {
              console.log("🚫 Preventing touch on disabled cell input");
              event.preventDefault();
              target.blur();
            }
          }
        };

        // Add all event listeners
        document.addEventListener("focusin", handleFocus, true);
        document.addEventListener("focus", handleFocus, true);
        container.addEventListener("click", handleClick, true);
        container.addEventListener("touchstart", handleTouchStart, true);

        // Store cleanup function
        container._nuclearKeyboardCleanup = () => {
          document.removeEventListener("focusin", handleFocus, true);
          document.removeEventListener("focus", handleFocus, true);
          container.removeEventListener("click", handleClick, true);
          container.removeEventListener("touchstart", handleTouchStart, true);
        };
      };

      // Apply nuclear prevention
      setTimeout(nuclearKeyboardPrevention, 100);
    }

    // 🚫 Disable blue selection highlighting for all devices
    const disableSelectionHighlight = () => {
      const container = document.getElementById(excelID);
      if (!container) return;

      // Prevent selection events
      const preventSelection = (event) => {
        // Allow selection only in input fields of masked cells
        const target = event.target;
        const isInputField =
          target.tagName === "INPUT" ||
          target.tagName === "TEXTAREA" ||
          target.contentEditable === "true";
        const isMaskedCell =
          target.closest('[style*="background-color: rgb(255, 255, 0)"]') ||
          target.closest('[style*="background: rgb(255, 255, 0)"]') ||
          target.style.backgroundColor?.includes("255, 255, 0");

        if (!isInputField || !isMaskedCell) {
          event.preventDefault();
          event.stopPropagation();
          return false;
        }
      };

      // Prevent drag selection
      const preventDragSelection = (event) => {
        const target = event.target;
        const isInputField =
          target.tagName === "INPUT" ||
          target.tagName === "TEXTAREA" ||
          target.contentEditable === "true";
        const isMaskedCell =
          target.closest('[style*="background-color: rgb(255, 255, 0)"]') ||
          target.closest('[style*="background: rgb(255, 255, 0)"]') ||
          target.style.backgroundColor?.includes("255, 255, 0");

        if (!isInputField || !isMaskedCell) {
          event.preventDefault();
          return false;
        }
      };

      // Add event listeners to prevent selection
      container.addEventListener("selectstart", preventSelection, true);
      container.addEventListener("dragstart", preventDragSelection, true);
      container.addEventListener(
        "mousedown",
        (event) => {
          // Prevent text selection on mousedown for non-input elements
          const target = event.target;
          const isInputField =
            target.tagName === "INPUT" ||
            target.tagName === "TEXTAREA" ||
            target.contentEditable === "true";
          const isMaskedCell =
            target.closest('[style*="background-color: rgb(255, 255, 0)"]') ||
            target.closest('[style*="background: rgb(255, 255, 0)"]') ||
            target.style.backgroundColor?.includes("255, 255, 0");

          if (!isInputField || !isMaskedCell) {
            event.preventDefault();
          }
        },
        true
      );

      // Store cleanup function
      container._selectionCleanup = () => {
        container.removeEventListener("selectstart", preventSelection, true);
        container.removeEventListener("dragstart", preventDragSelection, true);
      };
    };

    // Apply selection prevention after a short delay
    setTimeout(disableSelectionHighlight, 100);

    // 🔥 Aggressive selection removal - continuously monitor and remove selection elements
    const aggressiveSelectionRemoval = () => {
      const container = document.getElementById(excelID);
      if (!container) return;

      const removeSelectionElements = () => {
        // List of selection-related selectors to target
        const selectionSelectors = [
          '[class*="selection"]',
          '[class*="highlight"]',
          '[class*="range"]',
          ".univer-sheet-selection",
          ".univer-selection-overlay",
          ".univer-range-selection",
          ".univer-sheet-range-selection",
          ".univer-selection",
          ".univer-sheet-selection-shape",
          ".univer-selection-shape",
          ".univer-sheet-selection-control",
          ".univer-selection-control",
          ".univer-sheet-selection-area",
          ".univer-selection-area",
          ".univer-sheet-highlight",
          ".univer-highlight",
          ".univer-sheet-range-highlight",
          ".univer-range-highlight",
          ".univer-sheet-cell-selection",
          ".univer-cell-selection",
          ".univer-sheet-multi-selection",
          ".univer-multi-selection",
        ];

        selectionSelectors.forEach((selector) => {
          const elements = container.querySelectorAll(selector);
          elements.forEach((element) => {
            // Don't remove masked cells (yellow background)
            const isYellowCell =
              element.style.backgroundColor?.includes("255, 255, 0") ||
              element.style.background?.includes("255, 255, 0") ||
              element.closest('[style*="background-color: rgb(255, 255, 0)"]');

            if (!isYellowCell) {
              element.style.display = "none";
              element.style.visibility = "hidden";
              element.style.opacity = "0";
              element.style.background = "transparent";
              element.style.backgroundColor = "transparent";
              element.style.border = "none";
              element.style.pointerEvents = "none";
            }
          });
        });

        // Also target elements with blue-ish background colors
        const blueSelectors = [
          '[style*="background-color: rgb(173, 216, 230)"]',
          '[style*="background-color: lightblue"]',
          '[style*="background-color: #add8e6"]',
          '[style*="background: rgb(173, 216, 230)"]',
          '[style*="background: lightblue"]',
          '[style*="background: #add8e6"]',
          '[style*="background-color: rgba(173, 216, 230"]',
          '[style*="background: rgba(173, 216, 230"]',
        ];

        blueSelectors.forEach((selector) => {
          const elements = container.querySelectorAll(selector);
          elements.forEach((element) => {
            element.style.background = "transparent";
            element.style.backgroundColor = "transparent";
          });
        });
      };

      // Run immediately
      removeSelectionElements();

      // Set up a MutationObserver to watch for dynamically added selection elements
      const observer = new MutationObserver((mutations) => {
        let shouldRemove = false;
        mutations.forEach((mutation) => {
          if (mutation.type === "childList" && mutation.addedNodes.length > 0) {
            shouldRemove = true;
          }
          if (
            mutation.type === "attributes" &&
            (mutation.attributeName === "style" ||
              mutation.attributeName === "class")
          ) {
            shouldRemove = true;
          }
        });

        if (shouldRemove) {
          setTimeout(removeSelectionElements, 0);
        }
      });

      observer.observe(container, {
        childList: true,
        subtree: true,
        attributes: true,
        attributeFilter: ["style", "class"],
      });

      // Also run periodically as a fallback
      const intervalId = setInterval(removeSelectionElements, 100);

      // Store cleanup function
      container._aggressiveCleanup = () => {
        observer.disconnect();
        clearInterval(intervalId);
      };
    };

    // Apply aggressive selection removal
    setTimeout(aggressiveSelectionRemoval, 200);

    univerCreatedRef.current = true;

    return () => {
      // Cleanup mobile keyboard prevention
      if (isMobileDevice()) {
        const container = document.getElementById(excelID);
        if (container && container._nuclearKeyboardCleanup) {
          container._nuclearKeyboardCleanup();
          delete container._nuclearKeyboardCleanup;
        }
      }

      // Cleanup selection prevention
      const container = document.getElementById(excelID);
      if (container && container._selectionCleanup) {
        container._selectionCleanup();
        delete container._selectionCleanup;
      }

      // Cleanup aggressive selection removal
      if (container && container._aggressiveCleanup) {
        container._aggressiveCleanup();
        delete container._aggressiveCleanup;
      }

      univerAPIRef.current?.dispose?.();
      univerAPIRef.current = null;
      univerCreatedRef.current = false;
    };
  }, [workbookData, excelID]);

  return (
    <div>
      <div className="univer-container" id={excelID} />
      <ToastContainer transition={Zoom} />
    </div>
  );
}
