import { createSlice } from "@reduxjs/toolkit";
const initialState = {
  settour2: false,
};

export const Step2Slice = createSlice({
  name: "tour",
  initialState,
  reducers: {
    settour2totrue: (state, action) => {
      state.settour2 = true;
    },
    settour2tofalse: (state, action) => {
      state.settour2 = false;
    },
  },
});

export const { settour2totrue, settour2tofalse } =
  Step2Slice.actions;
export default Step2Slice.reducer;
